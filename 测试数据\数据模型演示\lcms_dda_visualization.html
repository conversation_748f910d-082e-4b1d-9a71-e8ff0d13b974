<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LC-MS/MS DDA Data Structure Educational Visualization</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .visualization-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .ms1-container {
            margin-bottom: 20px;
        }

        .ms2-container {
            height: 400px;
            border: 2px dashed #bdc3c7;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 1.1em;
            background: #f8f9fa;
        }

        .section-title {
            font-size: 1.4em;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        .control-group input, .control-group select {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .control-group input:focus, .control-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .info-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            grid-column: 1 / -1;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }

        .info-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .info-card p {
            color: #7f8c8d;
            line-height: 1.5;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background-color: #27ae60; }
        .status-waiting { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>LC-MS/MS DDA Data Structure</h1>
            <p>Interactive Educational Visualization - Explore the hierarchical relationship between MS1 and MS2 data in Data Dependent Acquisition (DDA) mode</p>
        </div>

        <div class="main-content">
            <div class="visualization-panel">
                <div class="ms1-container">
                    <h2 class="section-title">MS1 Data - 3D Chromatographic Surface</h2>
                    <div id="ms1-plot" style="height: 500px;"></div>
                </div>
                
                <div class="ms2-container" id="ms2-container">
                    <div style="text-align: center;">
                        <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
                        <div>Click on an MS1 data point above to view the corresponding MS2 fragmentation spectrum</div>
                    </div>
                </div>
            </div>

            <div class="control-panel">
                <h2 class="section-title">Controls & Settings</h2>
                
                <div class="control-group">
                    <label>Data Generation Mode:</label>
                    <select id="dataMode">
                        <option value="realistic">Realistic LC-MS/MS</option>
                        <option value="simple">Simplified Demo</option>
                        <option value="complex">Complex Mixture</option>
                    </select>
                </div>

                <div class="control-group">
                    <label>Number of Compounds:</label>
                    <input type="range" id="compoundCount" min="5" max="50" value="20">
                    <span id="compoundCountValue">20</span>
                </div>

                <div class="control-group">
                    <label>Retention Time Range (min):</label>
                    <input type="range" id="rtRange" min="5" max="60" value="30">
                    <span id="rtRangeValue">30</span>
                </div>

                <button class="btn" onclick="generateNewData()">Generate New Data</button>
                <button class="btn" onclick="resetView()">Reset View</button>
                <button class="btn" onclick="showTutorial()">Show Tutorial</button>

                <div class="control-group">
                    <label>Current Status:</label>
                    <div>
                        <span class="status-indicator status-ready"></span>
                        <span id="statusText">Ready - Click MS1 points to explore</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <h2 class="section-title">Understanding LC-MS/MS DDA Data Structure</h2>
            <div class="info-grid">
                <div class="info-card">
                    <h3>MS1 Data Characteristics</h3>
                    <p>MS1 data represents the full scan mass spectra showing all ionized compounds. The 3D visualization displays retention time (X), m/z (Y), and intensity (Z). Chromatographic peaks follow Gaussian distributions over time, representing compound elution from the LC column.</p>
                </div>
                <div class="info-card">
                    <h3>DDA Acquisition Strategy</h3>
                    <p>In Data Dependent Acquisition, the mass spectrometer automatically selects the most intense ions (TopN) from each MS1 scan for fragmentation. This creates a selective, non-continuous sampling pattern that differs from the smooth chromatographic profiles.</p>
                </div>
                <div class="info-card">
                    <h3>MS2 Data Structure</h3>
                    <p>MS2 spectra show fragmentation patterns of selected precursor ions. The intensity distribution is NOT Gaussian due to the selective nature of DDA. Each spectrum provides structural information about the fragmented compound.</p>
                </div>
                <div class="info-card">
                    <h3>Data Relationships</h3>
                    <p>MS2 precursor m/z values correspond to specific points in the MS1 data. However, due to dynamic exclusion and TopN selection, not all MS1 peaks will have corresponding MS2 spectra, creating the characteristic discontinuous pattern of DDA data.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables for data storage
        let ms1Data = null;
        let ms2Data = null;
        let currentPrecursor = null;

        // Initialize the visualization
        document.addEventListener('DOMContentLoaded', function() {
            initializeControls();
            generateNewData();
        });

        // Control initialization
        function initializeControls() {
            document.getElementById('compoundCount').addEventListener('input', function() {
                document.getElementById('compoundCountValue').textContent = this.value;
            });
            
            document.getElementById('rtRange').addEventListener('input', function() {
                document.getElementById('rtRangeValue').textContent = this.value;
            });
        }

        // Status update function
        function updateStatus(message, type = 'ready') {
            const statusText = document.getElementById('statusText');
            const indicator = document.querySelector('.status-indicator');

            statusText.textContent = message;
            indicator.className = `status-indicator status-${type}`;
        }

        // MS1 Data Generation Functions
        function generateMS1Data() {
            const compoundCount = parseInt(document.getElementById('compoundCount').value);
            const rtRange = parseInt(document.getElementById('rtRange').value);
            const dataMode = document.getElementById('dataMode').value;

            let compounds = [];

            // Generate compound parameters based on mode
            for (let i = 0; i < compoundCount; i++) {
                const compound = {
                    id: i,
                    mz: generateRealisticMZ(),
                    rtCenter: Math.random() * rtRange,
                    rtWidth: 0.2 + Math.random() * 0.8, // Peak width in minutes
                    maxIntensity: generateRealisticIntensity(dataMode),
                    charge: Math.random() < 0.7 ? 1 : (Math.random() < 0.8 ? 2 : 3)
                };
                compounds.push(compound);
            }

            // Generate data points
            const retentionTime = [];
            const mz = [];
            const intensity = [];
            const compoundIds = [];

            // Time points (every 0.1 minutes)
            const timeStep = 0.1;
            const timePoints = Math.ceil(rtRange / timeStep);

            for (let t = 0; t < timePoints; t++) {
                const currentTime = t * timeStep;

                for (let comp of compounds) {
                    // Calculate Gaussian peak intensity at current time
                    const gaussianIntensity = comp.maxIntensity *
                        Math.exp(-0.5 * Math.pow((currentTime - comp.rtCenter) / (comp.rtWidth / 4), 2));

                    // Only include points above noise threshold
                    if (gaussianIntensity > comp.maxIntensity * 0.01) {
                        retentionTime.push(currentTime);
                        mz.push(comp.mz);
                        intensity.push(gaussianIntensity + Math.random() * gaussianIntensity * 0.1); // Add noise
                        compoundIds.push(comp.id);

                        // Add isotope peaks for realistic data
                        if (Math.random() < 0.6) { // 60% chance of visible isotope
                            const isotopeMZ = comp.mz + 1.003 / comp.charge; // C13 isotope
                            const isotopeIntensity = gaussianIntensity * (0.1 + Math.random() * 0.3);

                            retentionTime.push(currentTime);
                            mz.push(isotopeMZ);
                            intensity.push(isotopeIntensity);
                            compoundIds.push(comp.id);
                        }
                    }
                }
            }

            return {
                retentionTime: retentionTime,
                mz: mz,
                intensity: intensity,
                compoundIds: compoundIds,
                compounds: compounds
            };
        }

        function generateRealisticMZ() {
            // Generate realistic m/z values for small molecules and peptides
            const categories = [
                { range: [100, 300], weight: 0.3 },   // Small molecules
                { range: [300, 800], weight: 0.4 },   // Medium molecules
                { range: [800, 1500], weight: 0.2 },  // Large molecules/peptides
                { range: [1500, 3000], weight: 0.1 }  // Large peptides/proteins
            ];

            const rand = Math.random();
            let cumWeight = 0;

            for (let cat of categories) {
                cumWeight += cat.weight;
                if (rand < cumWeight) {
                    return cat.range[0] + Math.random() * (cat.range[1] - cat.range[0]);
                }
            }

            return 200 + Math.random() * 800; // Fallback
        }

        function generateRealisticIntensity(mode) {
            let baseIntensity;

            switch (mode) {
                case 'simple':
                    baseIntensity = 1000 + Math.random() * 9000;
                    break;
                case 'complex':
                    // More dynamic range in complex mode
                    baseIntensity = Math.pow(10, 2 + Math.random() * 4); // 100 to 100,000
                    break;
                default: // realistic
                    // Log-normal distribution for realistic intensity
                    const logMean = 3.5;
                    const logStd = 1.2;
                    const logIntensity = logMean + logStd * (Math.random() + Math.random() + Math.random() + Math.random() - 2);
                    baseIntensity = Math.exp(logIntensity);
                    break;
            }

            return Math.max(100, baseIntensity); // Minimum intensity threshold
        }

        // MS1 3D Visualization Functions
        function createMS1Plot(data) {
            const trace = {
                x: data.retentionTime,
                y: data.mz,
                z: data.intensity,
                mode: 'markers',
                marker: {
                    size: 3,
                    color: data.intensity,
                    colorscale: [
                        [0, 'rgb(68, 1, 84)'],      // Dark purple
                        [0.2, 'rgb(59, 82, 139)'],  // Dark blue
                        [0.4, 'rgb(33, 144, 140)'], // Teal
                        [0.6, 'rgb(93, 201, 99)'],  // Green
                        [0.8, 'rgb(253, 231, 37)'], // Yellow
                        [1, 'rgb(255, 255, 255)']   // White
                    ],
                    colorbar: {
                        title: 'Intensity',
                        titleside: 'right'
                    },
                    opacity: 0.8,
                    line: {
                        width: 0.5,
                        color: 'rgba(0,0,0,0.3)'
                    }
                },
                type: 'scatter3d',
                name: 'MS1 Data Points',
                hovertemplate:
                    '<b>Retention Time:</b> %{x:.2f} min<br>' +
                    '<b>m/z:</b> %{y:.4f}<br>' +
                    '<b>Intensity:</b> %{z:.0f}<br>' +
                    '<extra></extra>'
            };

            const layout = {
                title: {
                    text: 'MS1 Data: 3D Chromatographic Surface',
                    font: { size: 16, color: '#2c3e50' }
                },
                scene: {
                    xaxis: {
                        title: 'Retention Time (min)',
                        titlefont: { color: '#2c3e50' },
                        tickfont: { color: '#7f8c8d' }
                    },
                    yaxis: {
                        title: 'm/z',
                        titlefont: { color: '#2c3e50' },
                        tickfont: { color: '#7f8c8d' }
                    },
                    zaxis: {
                        title: 'Intensity',
                        titlefont: { color: '#2c3e50' },
                        tickfont: { color: '#7f8c8d' }
                    },
                    camera: {
                        eye: { x: 1.5, y: 1.5, z: 1.5 }
                    },
                    bgcolor: 'rgba(240, 240, 240, 0.8)'
                },
                margin: { l: 0, r: 0, b: 0, t: 40 },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)'
            };

            const config = {
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
                displaylogo: false,
                responsive: true
            };

            Plotly.newPlot('ms1-plot', [trace], layout, config);

            // Add click event listener for MS1 plot
            document.getElementById('ms1-plot').on('plotly_click', function(data) {
                if (data.points.length > 0) {
                    const point = data.points[0];
                    const rt = point.x;
                    const mz = point.y;
                    const intensity = point.z;

                    updateStatus(`Selected: RT=${rt.toFixed(2)}min, m/z=${mz.toFixed(4)}`, 'waiting');

                    // Find and display corresponding MS2 spectrum
                    showMS2Spectrum(mz, rt, intensity);
                }
            });
        }

        // MS2 Visualization Functions
        function showMS2Spectrum(precursorMZ, rt, precursorIntensity) {
            // Generate MS2 spectrum for the selected precursor
            const ms2Spectrum = generateMS2Spectrum(precursorMZ, precursorIntensity);

            if (ms2Spectrum && ms2Spectrum.fragmentMZ.length > 0) {
                createMS2Plot(ms2Spectrum, precursorMZ, rt);
                updateStatus(`Showing MS2 for m/z ${precursorMZ.toFixed(4)} at RT ${rt.toFixed(2)}min`, 'ready');
            } else {
                // Show message when no MS2 available (realistic DDA behavior)
                document.getElementById('ms2-container').innerHTML = `
                    <div style="text-align: center; color: #e67e22;">
                        <div style="font-size: 2em; margin-bottom: 10px;">⚠️</div>
                        <div><strong>No MS2 spectrum available</strong></div>
                        <div style="margin-top: 10px; font-size: 0.9em;">
                            Precursor m/z: ${precursorMZ.toFixed(4)}<br>
                            RT: ${rt.toFixed(2)} min<br>
                            <em>This ion was not selected for fragmentation in DDA mode</em>
                        </div>
                    </div>
                `;
                updateStatus(`No MS2 available for m/z ${precursorMZ.toFixed(4)} (not selected by DDA)`, 'ready');
            }
        }

        function createMS2Plot(spectrum, precursorMZ, rt) {
            const trace = {
                x: spectrum.fragmentMZ,
                y: spectrum.intensity,
                type: 'bar',
                marker: {
                    color: spectrum.intensity,
                    colorscale: [
                        [0, 'rgb(49, 130, 189)'],
                        [0.5, 'rgb(107, 174, 214)'],
                        [1, 'rgb(158, 202, 225)']
                    ],
                    line: {
                        color: 'rgb(8, 48, 107)',
                        width: 1
                    }
                },
                name: 'Fragment Ions',
                hovertemplate:
                    '<b>Fragment m/z:</b> %{x:.4f}<br>' +
                    '<b>Intensity:</b> %{y:.0f}<br>' +
                    '<extra></extra>'
            };

            const layout = {
                title: {
                    text: `MS2 Spectrum - Precursor m/z: ${precursorMZ.toFixed(4)} (RT: ${rt.toFixed(2)}min)`,
                    font: { size: 14, color: '#2c3e50' }
                },
                xaxis: {
                    title: 'Fragment m/z',
                    titlefont: { color: '#2c3e50' },
                    tickfont: { color: '#7f8c8d' },
                    showgrid: true,
                    gridcolor: 'rgba(0,0,0,0.1)'
                },
                yaxis: {
                    title: 'Relative Intensity (%)',
                    titlefont: { color: '#2c3e50' },
                    tickfont: { color: '#7f8c8d' },
                    showgrid: true,
                    gridcolor: 'rgba(0,0,0,0.1)'
                },
                margin: { l: 60, r: 20, b: 60, t: 60 },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(248, 249, 250, 0.8)',
                bargap: 0.1
            };

            const config = {
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'zoom2d', 'zoomIn2d', 'zoomOut2d'],
                displaylogo: false,
                responsive: true
            };

            // Clear the container and create new plot
            document.getElementById('ms2-container').innerHTML = '<div id="ms2-plot" style="height: 100%; width: 100%;"></div>';

            setTimeout(() => {
                Plotly.newPlot('ms2-plot', [trace], layout, config);
            }, 100);
        }

        function resetView() {
            updateStatus('Resetting view...', 'waiting');
            if (ms1Data) {
                createMS1Plot(ms1Data);
                document.getElementById('ms2-container').innerHTML = `
                    <div style="text-align: center;">
                        <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
                        <div>Click on an MS1 data point above to view the corresponding MS2 fragmentation spectrum</div>
                    </div>
                `;
            }
            setTimeout(() => {
                updateStatus('View reset complete', 'ready');
            }, 500);
        }

        function showTutorial() {
            const tutorialText = `
LC-MS/MS DDA Data Structure Tutorial:

1. MS1 Data (3D Plot Above):
   - X-axis: Retention Time (compounds elute over time)
   - Y-axis: m/z (mass-to-charge ratio)
   - Z-axis: Intensity (signal strength)
   - Colors represent intensity levels

2. Interaction:
   - Click any point in the 3D plot to see its MS2 spectrum
   - Use mouse to rotate, zoom, and pan the 3D view
   - Points represent detected ions at specific RT and m/z

3. DDA Process:
   - Mass spectrometer selects intense ions for fragmentation
   - Not all MS1 points have MS2 spectra (selective sampling)
   - MS2 shows structural information through fragmentation

4. Data Characteristics:
   - MS1: Continuous chromatographic profiles (Gaussian peaks)
   - MS2: Discontinuous, selective fragmentation spectra
            `;
            alert(tutorialText);
        }

        // Main data generation function
        function generateNewData() {
            updateStatus('Generating new MS1 data...', 'waiting');

            setTimeout(() => {
                try {
                    // Generate MS1 data
                    ms1Data = generateMS1Data();

                    // Create the 3D plot
                    createMS1Plot(ms1Data);

                    // Reset MS2 display
                    document.getElementById('ms2-container').innerHTML = `
                        <div style="text-align: center;">
                            <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
                            <div>Click on an MS1 data point above to view the corresponding MS2 fragmentation spectrum</div>
                        </div>
                    `;

                    updateStatus(`Generated ${ms1Data.retentionTime.length} MS1 data points - Click to explore`, 'ready');
                } catch (error) {
                    console.error('Error generating data:', error);
                    updateStatus('Error generating data', 'error');
                }
            }, 100);
        }
    </script>
</body>
</html>
