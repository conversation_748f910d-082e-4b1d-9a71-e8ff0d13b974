<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LC-MS/MS DDA Data Structure Educational Visualization</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .visualization-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .ms1-container {
            margin-bottom: 20px;
        }

        .ms2-container {
            height: 400px;
            border: 2px dashed #bdc3c7;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 1.1em;
            background: #f8f9fa;
        }

        .section-title {
            font-size: 1.4em;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        .control-group input, .control-group select {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .control-group input:focus, .control-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .info-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            grid-column: 1 / -1;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }

        .info-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .info-card p {
            color: #7f8c8d;
            line-height: 1.5;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background-color: #27ae60; }
        .status-waiting { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>LC-MS/MS DDA Data Structure</h1>
            <p>Interactive Educational Visualization - Explore the hierarchical relationship between MS1 and MS2 data in Data Dependent Acquisition (DDA) mode</p>
        </div>

        <div class="main-content">
            <div class="visualization-panel">
                <div class="ms1-container">
                    <h2 class="section-title">MS1 Data - 3D Chromatographic Surface</h2>
                    <div id="ms1-plot" style="height: 500px;"></div>
                </div>
                
                <div class="ms2-container" id="ms2-container">
                    <div style="text-align: center;">
                        <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
                        <div>Click on an MS1 data point above to view the corresponding MS2 fragmentation spectrum</div>
                    </div>
                </div>
            </div>

            <div class="control-panel">
                <h2 class="section-title">Controls & Settings</h2>
                
                <div class="control-group">
                    <label>📊 Data Generation Mode:</label>
                    <select id="dataMode" onchange="updateModeDescription()">
                        <option value="realistic">Realistic LC-MS/MS</option>
                        <option value="simple">Simplified Demo</option>
                        <option value="complex">Complex Mixture</option>
                    </select>
                    <div id="modeDescription" style="font-size: 0.8em; color: #7f8c8d; margin-top: 5px;">
                        Realistic intensity distribution with log-normal characteristics
                    </div>
                </div>

                <div class="control-group">
                    <label>🧪 Number of Compounds:</label>
                    <input type="range" id="compoundCount" min="5" max="50" value="20">
                    <span id="compoundCountValue">20</span>
                    <div style="font-size: 0.8em; color: #7f8c8d; margin-top: 5px;">
                        More compounds = more complex chromatogram
                    </div>
                </div>

                <div class="control-group">
                    <label>⏱️ Retention Time Range (min):</label>
                    <input type="range" id="rtRange" min="5" max="60" value="30">
                    <span id="rtRangeValue">30</span>
                    <div style="font-size: 0.8em; color: #7f8c8d; margin-top: 5px;">
                        Longer gradients = better separation
                    </div>
                </div>

                <div class="control-group">
                    <label>🎯 DDA Parameters:</label>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                        <div>
                            <label style="font-size: 0.9em;">TopN:</label>
                            <select id="topN">
                                <option value="3">Top 3</option>
                                <option value="5" selected>Top 5</option>
                                <option value="10">Top 10</option>
                            </select>
                        </div>
                        <div>
                            <label style="font-size: 0.9em;">Exclusion (s):</label>
                            <select id="exclusionTime">
                                <option value="15">15s</option>
                                <option value="30" selected>30s</option>
                                <option value="60">60s</option>
                            </select>
                        </div>
                    </div>
                    <div style="font-size: 0.8em; color: #7f8c8d;">
                        Affects MS2 selection probability
                    </div>
                </div>

                <button class="btn" onclick="generateNewData()">🔄 Generate New Data</button>
                <button class="btn" onclick="resetView()">🏠 Reset View</button>
                <button class="btn" onclick="showTutorial()">📚 Show Tutorial</button>
                <button class="btn" onclick="showStatistics()" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">📈 Show Statistics</button>

                <div class="control-group">
                    <label>📊 Visualization Options:</label>
                    <div style="margin-bottom: 10px;">
                        <label style="display: flex; align-items: center; font-size: 0.9em;">
                            <input type="checkbox" id="showIsotopes" checked style="margin-right: 8px;">
                            Show Isotope Peaks
                        </label>
                        <label style="display: flex; align-items: center; font-size: 0.9em;">
                            <input type="checkbox" id="showNoise" checked style="margin-right: 8px;">
                            Add Realistic Noise
                        </label>
                    </div>
                </div>

                <div class="control-group">
                    <label>📍 Current Status:</label>
                    <div>
                        <span class="status-indicator status-ready"></span>
                        <span id="statusText">Ready - Click MS1 points to explore</span>
                    </div>
                </div>

                <div class="control-group" id="dataStats" style="display: none;">
                    <label>📈 Data Statistics:</label>
                    <div id="statsContent" style="font-size: 0.9em; color: #2c3e50; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <!-- Statistics will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <h2 class="section-title">Understanding LC-MS/MS DDA Data Structure</h2>
            <div class="info-grid">
                <div class="info-card">
                    <h3>📊 MS1 Data Characteristics</h3>
                    <p>MS1 data represents the full scan mass spectra showing all ionized compounds. The 3D visualization displays retention time (X), m/z (Y), and intensity (Z). Chromatographic peaks follow <strong>Gaussian distributions</strong> over time, representing compound elution from the LC column.</p>
                    <div style="margin-top: 10px; padding: 8px; background: #e8f4fd; border-radius: 5px; font-size: 0.9em;">
                        <strong>Key Point:</strong> The continuous nature of MS1 data reflects the physical separation process in liquid chromatography.
                    </div>
                </div>
                <div class="info-card">
                    <h3>🎯 DDA Acquisition Strategy</h3>
                    <p>In Data Dependent Acquisition, the mass spectrometer automatically selects the most intense ions (<strong>TopN strategy</strong>) from each MS1 scan for fragmentation. This creates a selective, non-continuous sampling pattern that differs from the smooth chromatographic profiles.</p>
                    <div style="margin-top: 10px; padding: 8px; background: #fef9e7; border-radius: 5px; font-size: 0.9em;">
                        <strong>Real-world Impact:</strong> Typical TopN = 5-10 ions per scan, with dynamic exclusion preventing repeated selection of the same ion.
                    </div>
                </div>
                <div class="info-card">
                    <h3>🔬 MS2 Data Structure</h3>
                    <p>MS2 spectra show fragmentation patterns of selected precursor ions. The intensity distribution is <strong>NOT Gaussian</strong> due to the selective nature of DDA. Each spectrum provides structural information about the fragmented compound.</p>
                    <div style="margin-top: 10px; padding: 8px; background: #f0f9ff; border-radius: 5px; font-size: 0.9em;">
                        <strong>Educational Note:</strong> Fragment ion intensities follow exponential decay patterns, not chromatographic profiles.
                    </div>
                </div>
                <div class="info-card">
                    <h3>🔗 Data Relationships</h3>
                    <p>MS2 precursor m/z values correspond to specific points in the MS1 data. However, due to <strong>dynamic exclusion</strong> and TopN selection, not all MS1 peaks will have corresponding MS2 spectra, creating the characteristic discontinuous pattern of DDA data.</p>
                    <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 5px; font-size: 0.9em;">
                        <strong>Try It:</strong> Click different intensity levels in the MS1 plot - notice that low-intensity points rarely have MS2 spectra!
                    </div>
                </div>
                <div class="info-card" style="grid-column: 1 / -1; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <h3>🎓 Educational Objectives</h3>
                    <p>This visualization helps students understand:</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>The hierarchical relationship between MS1 survey scans and MS2 fragmentation spectra</li>
                        <li>How DDA acquisition strategy affects data completeness and structure</li>
                        <li>The difference between continuous chromatographic data and selective MS/MS data</li>
                        <li>Real-world limitations of mass spectrometry data acquisition</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables for data storage
        let ms1Data = null;
        let ms2Data = null;
        let currentPrecursor = null;

        // Initialize the visualization
        document.addEventListener('DOMContentLoaded', function() {
            initializeControls();
            generateNewData();

            // Show welcome message after a short delay
            setTimeout(() => {
                updateStatus('Welcome! Try clicking the "Show Tutorial" button to get started 📚', 'ready');
            }, 2000);

            // Log performance metrics for educational purposes
            setTimeout(logPerformanceMetrics, 3000);
        });

        // Control initialization and handlers
        function initializeControls() {
            document.getElementById('compoundCount').addEventListener('input', function() {
                document.getElementById('compoundCountValue').textContent = this.value;
            });

            document.getElementById('rtRange').addEventListener('input', function() {
                document.getElementById('rtRangeValue').textContent = this.value;
            });

            // Initialize mode description
            updateModeDescription();
        }

        function updateModeDescription() {
            const mode = document.getElementById('dataMode').value;
            const descriptions = {
                'realistic': 'Realistic intensity distribution with log-normal characteristics and isotope patterns',
                'simple': 'Simplified data with uniform intensity distribution for educational clarity',
                'complex': 'Complex mixture with high dynamic range (100-100,000 intensity units)'
            };
            document.getElementById('modeDescription').textContent = descriptions[mode];
        }

        function showStatistics() {
            if (!ms1Data) {
                alert('Please generate data first!');
                return;
            }

            const statsDiv = document.getElementById('dataStats');
            const statsContent = document.getElementById('statsContent');

            // Calculate statistics
            const totalPoints = ms1Data.retentionTime.length;
            const uniqueCompounds = new Set(ms1Data.compoundIds).size;
            const avgIntensity = ms1Data.intensity.reduce((a, b) => a + b, 0) / totalPoints;
            const maxIntensity = Math.max(...ms1Data.intensity);
            const minIntensity = Math.min(...ms1Data.intensity);

            // Simulate MS2 selection
            const selectedPrecursors = simulateDDASelection(ms1Data);
            const ms2Coverage = (selectedPrecursors.length / totalPoints * 100).toFixed(1);

            statsContent.innerHTML = `
                <strong>MS1 Data:</strong><br>
                • Total data points: ${totalPoints.toLocaleString()}<br>
                • Unique compounds: ${uniqueCompounds}<br>
                • Intensity range: ${minIntensity.toFixed(0)} - ${maxIntensity.toFixed(0)}<br>
                • Average intensity: ${avgIntensity.toFixed(0)}<br><br>

                <strong>DDA Simulation:</strong><br>
                • Precursors selected: ${selectedPrecursors.length}<br>
                • MS2 coverage: ${ms2Coverage}% of MS1 points<br>
                • TopN setting: ${document.getElementById('topN').value}<br>
                • Exclusion time: ${document.getElementById('exclusionTime').value}s
            `;

            statsDiv.style.display = 'block';

            // Hide after 10 seconds
            setTimeout(() => {
                statsDiv.style.display = 'none';
            }, 10000);
        }

        // Status update function
        function updateStatus(message, type = 'ready') {
            const statusText = document.getElementById('statusText');
            const indicator = document.querySelector('.status-indicator');

            statusText.textContent = message;
            indicator.className = `status-indicator status-${type}`;
        }

        // MS1 Data Generation Functions
        function generateMS1Data() {
            const compoundCount = parseInt(document.getElementById('compoundCount').value);
            const rtRange = parseInt(document.getElementById('rtRange').value);
            const dataMode = document.getElementById('dataMode').value;

            let compounds = [];

            // Generate compound parameters based on mode
            for (let i = 0; i < compoundCount; i++) {
                const compound = {
                    id: i,
                    mz: generateRealisticMZ(),
                    rtCenter: Math.random() * rtRange,
                    rtWidth: 0.2 + Math.random() * 0.8, // Peak width in minutes
                    maxIntensity: generateRealisticIntensity(dataMode),
                    charge: Math.random() < 0.7 ? 1 : (Math.random() < 0.8 ? 2 : 3)
                };
                compounds.push(compound);
            }

            // Generate data points
            const retentionTime = [];
            const mz = [];
            const intensity = [];
            const compoundIds = [];

            // Time points (every 0.1 minutes)
            const timeStep = 0.1;
            const timePoints = Math.ceil(rtRange / timeStep);

            for (let t = 0; t < timePoints; t++) {
                const currentTime = t * timeStep;

                for (let comp of compounds) {
                    // Calculate Gaussian peak intensity at current time
                    const gaussianIntensity = comp.maxIntensity *
                        Math.exp(-0.5 * Math.pow((currentTime - comp.rtCenter) / (comp.rtWidth / 4), 2));

                    // Only include points above noise threshold
                    if (gaussianIntensity > comp.maxIntensity * 0.01) {
                        const showNoise = document.getElementById('showNoise') ?
                            document.getElementById('showNoise').checked : true;
                        const noiseLevel = showNoise ? Math.random() * gaussianIntensity * 0.1 : 0;

                        retentionTime.push(currentTime);
                        mz.push(comp.mz);
                        intensity.push(gaussianIntensity + noiseLevel);
                        compoundIds.push(comp.id);

                        // Add isotope peaks for realistic data (if enabled)
                        const showIsotopes = document.getElementById('showIsotopes') ?
                            document.getElementById('showIsotopes').checked : true;
                        if (showIsotopes && Math.random() < 0.6) { // 60% chance of visible isotope
                            const isotopeMZ = comp.mz + 1.003 / comp.charge; // C13 isotope
                            const isotopeIntensity = gaussianIntensity * (0.1 + Math.random() * 0.3);
                            const showNoise = document.getElementById('showNoise') ?
                                document.getElementById('showNoise').checked : true;
                            const isotopeNoise = showNoise ? Math.random() * isotopeIntensity * 0.1 : 0;

                            retentionTime.push(currentTime);
                            mz.push(isotopeMZ);
                            intensity.push(isotopeIntensity + isotopeNoise);
                            compoundIds.push(comp.id);
                        }
                    }
                }
            }

            return {
                retentionTime: retentionTime,
                mz: mz,
                intensity: intensity,
                compoundIds: compoundIds,
                compounds: compounds
            };
        }

        function generateRealisticMZ() {
            // Generate realistic m/z values for small molecules and peptides
            const categories = [
                { range: [100, 300], weight: 0.3 },   // Small molecules
                { range: [300, 800], weight: 0.4 },   // Medium molecules
                { range: [800, 1500], weight: 0.2 },  // Large molecules/peptides
                { range: [1500, 3000], weight: 0.1 }  // Large peptides/proteins
            ];

            const rand = Math.random();
            let cumWeight = 0;

            for (let cat of categories) {
                cumWeight += cat.weight;
                if (rand < cumWeight) {
                    return cat.range[0] + Math.random() * (cat.range[1] - cat.range[0]);
                }
            }

            return 200 + Math.random() * 800; // Fallback
        }

        function generateRealisticIntensity(mode) {
            let baseIntensity;

            switch (mode) {
                case 'simple':
                    baseIntensity = 1000 + Math.random() * 9000;
                    break;
                case 'complex':
                    // More dynamic range in complex mode
                    baseIntensity = Math.pow(10, 2 + Math.random() * 4); // 100 to 100,000
                    break;
                default: // realistic
                    // Log-normal distribution for realistic intensity
                    const logMean = 3.5;
                    const logStd = 1.2;
                    const logIntensity = logMean + logStd * (Math.random() + Math.random() + Math.random() + Math.random() - 2);
                    baseIntensity = Math.exp(logIntensity);
                    break;
            }

            return Math.max(100, baseIntensity); // Minimum intensity threshold
        }

        // MS2 Data Generation Functions
        function generateMS2Spectrum(precursorMZ, precursorIntensity) {
            // Simulate DDA selection criteria
            // Only high-intensity ions are selected for MS2 (TopN strategy)
            const intensityThreshold = 5000; // Minimum intensity for MS2 selection
            const selectionProbability = Math.min(1, precursorIntensity / 50000); // Higher intensity = higher selection chance

            // Random selection based on DDA parameters
            if (precursorIntensity < intensityThreshold || Math.random() > selectionProbability) {
                return null; // No MS2 spectrum generated (realistic DDA behavior)
            }

            // Generate fragment ions
            const fragmentMZ = [];
            const intensity = [];

            // Number of fragments (realistic range)
            const numFragments = 5 + Math.floor(Math.random() * 15);

            for (let i = 0; i < numFragments; i++) {
                // Fragment m/z should be less than precursor m/z
                const fragMZ = 50 + Math.random() * (precursorMZ - 50);

                // Fragment intensity (non-Gaussian distribution)
                // Use exponential decay with some high-intensity fragments
                let fragIntensity;
                if (Math.random() < 0.1) {
                    // 10% chance of high-intensity fragment (base peak region)
                    fragIntensity = 80 + Math.random() * 20; // 80-100% relative intensity
                } else if (Math.random() < 0.3) {
                    // 30% chance of medium-intensity fragment
                    fragIntensity = 30 + Math.random() * 50; // 30-80% relative intensity
                } else {
                    // 60% chance of low-intensity fragment
                    fragIntensity = 1 + Math.random() * 29; // 1-30% relative intensity
                }

                fragmentMZ.push(fragMZ);
                intensity.push(fragIntensity);
            }

            // Sort by m/z for better visualization
            const combined = fragmentMZ.map((mz, i) => ({ mz, intensity: intensity[i] }));
            combined.sort((a, b) => a.mz - b.mz);

            return {
                fragmentMZ: combined.map(item => item.mz),
                intensity: combined.map(item => item.intensity),
                precursorMZ: precursorMZ,
                precursorIntensity: precursorIntensity
            };
        }

        // DDA Simulation Functions
        function simulateDDASelection(ms1Data) {
            // Get current DDA parameters from controls
            const topN = parseInt(document.getElementById('topN').value);
            const exclusionDuration = parseInt(document.getElementById('exclusionTime').value);
            const exclusionList = new Map(); // Track excluded m/z values

            const selectedPrecursors = [];
            const rtStep = 0.5; // RT window for each "scan"
            const rtRange = Math.max(...ms1Data.retentionTime);

            for (let rt = 0; rt < rtRange; rt += rtStep) {
                // Get ions in current RT window
                const currentIons = [];
                for (let i = 0; i < ms1Data.retentionTime.length; i++) {
                    if (Math.abs(ms1Data.retentionTime[i] - rt) <= rtStep / 2) {
                        currentIons.push({
                            index: i,
                            mz: ms1Data.mz[i],
                            intensity: ms1Data.intensity[i],
                            rt: ms1Data.retentionTime[i]
                        });
                    }
                }

                // Remove excluded ions
                const availableIons = currentIons.filter(ion => {
                    const key = Math.round(ion.mz * 10) / 10; // Round to 0.1 Da
                    const exclusionTime = exclusionList.get(key);
                    return !exclusionTime || (rt - exclusionTime) > exclusionDuration;
                });

                // Sort by intensity and select TopN
                availableIons.sort((a, b) => b.intensity - a.intensity);
                const selected = availableIons.slice(0, topN);

                // Add to exclusion list and selected precursors
                selected.forEach(ion => {
                    const key = Math.round(ion.mz * 10) / 10;
                    exclusionList.set(key, rt);
                    selectedPrecursors.push(ion);
                });
            }

            return selectedPrecursors;
        }

        // MS1 3D Visualization Functions
        function createMS1Plot(data) {
            const trace = {
                x: data.retentionTime,
                y: data.mz,
                z: data.intensity,
                mode: 'markers',
                marker: {
                    size: 3,
                    color: data.intensity,
                    colorscale: [
                        [0, 'rgb(68, 1, 84)'],      // Dark purple
                        [0.2, 'rgb(59, 82, 139)'],  // Dark blue
                        [0.4, 'rgb(33, 144, 140)'], // Teal
                        [0.6, 'rgb(93, 201, 99)'],  // Green
                        [0.8, 'rgb(253, 231, 37)'], // Yellow
                        [1, 'rgb(255, 255, 255)']   // White
                    ],
                    colorbar: {
                        title: 'Intensity',
                        titleside: 'right'
                    },
                    opacity: 0.8,
                    line: {
                        width: 0.5,
                        color: 'rgba(0,0,0,0.3)'
                    }
                },
                type: 'scatter3d',
                name: 'MS1 Data Points',
                hovertemplate:
                    '<b>Retention Time:</b> %{x:.2f} min<br>' +
                    '<b>m/z:</b> %{y:.4f}<br>' +
                    '<b>Intensity:</b> %{z:.0f}<br>' +
                    '<extra></extra>'
            };

            const layout = {
                title: {
                    text: 'MS1 Data: 3D Chromatographic Surface',
                    font: { size: 16, color: '#2c3e50' }
                },
                scene: {
                    xaxis: {
                        title: 'Retention Time (min)',
                        titlefont: { color: '#2c3e50' },
                        tickfont: { color: '#7f8c8d' }
                    },
                    yaxis: {
                        title: 'm/z',
                        titlefont: { color: '#2c3e50' },
                        tickfont: { color: '#7f8c8d' }
                    },
                    zaxis: {
                        title: 'Intensity',
                        titlefont: { color: '#2c3e50' },
                        tickfont: { color: '#7f8c8d' }
                    },
                    camera: {
                        eye: { x: 1.5, y: 1.5, z: 1.5 }
                    },
                    bgcolor: 'rgba(240, 240, 240, 0.8)'
                },
                margin: { l: 0, r: 0, b: 0, t: 40 },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)'
            };

            const config = {
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
                displaylogo: false,
                responsive: true
            };

            Plotly.newPlot('ms1-plot', [trace], layout, config);

            // Add click event listener for MS1 plot
            document.getElementById('ms1-plot').on('plotly_click', function(data) {
                if (data.points.length > 0) {
                    const point = data.points[0];
                    const rt = point.x;
                    const mz = point.y;
                    const intensity = point.z;

                    updateStatus(`Selected: RT=${rt.toFixed(2)}min, m/z=${mz.toFixed(4)}`, 'waiting');

                    // Find and display corresponding MS2 spectrum
                    showMS2Spectrum(mz, rt, intensity);
                }
            });
        }

        // MS2 Visualization Functions
        function showMS2Spectrum(precursorMZ, rt, precursorIntensity) {
            // Highlight the selected point
            highlightSelectedPoint(rt, precursorMZ, precursorIntensity);

            // Generate MS2 spectrum for the selected precursor
            const ms2Spectrum = generateMS2Spectrum(precursorMZ, precursorIntensity);

            if (ms2Spectrum && ms2Spectrum.fragmentMZ.length > 0) {
                createMS2Plot(ms2Spectrum, precursorMZ, rt);
                updateStatus(`Showing MS2 for m/z ${precursorMZ.toFixed(4)} at RT ${rt.toFixed(2)}min`, 'ready');
            } else {
                // Show message when no MS2 available (realistic DDA behavior)
                document.getElementById('ms2-container').innerHTML = `
                    <div style="text-align: center; color: #e67e22;">
                        <div style="font-size: 2em; margin-bottom: 10px;">⚠️</div>
                        <div><strong>No MS2 spectrum available</strong></div>
                        <div style="margin-top: 10px; font-size: 0.9em;">
                            Precursor m/z: ${precursorMZ.toFixed(4)}<br>
                            RT: ${rt.toFixed(2)} min<br>
                            Intensity: ${precursorIntensity.toFixed(0)}<br>
                            <em>This ion was not selected for fragmentation in DDA mode</em><br>
                            <small>(Intensity too low or excluded by dynamic exclusion)</small>
                        </div>
                    </div>
                `;
                updateStatus(`No MS2 available for m/z ${precursorMZ.toFixed(4)} (not selected by DDA)`, 'ready');
            }
        }

        function createMS2Plot(spectrum, precursorMZ, rt) {
            const trace = {
                x: spectrum.fragmentMZ,
                y: spectrum.intensity,
                type: 'bar',
                marker: {
                    color: spectrum.intensity,
                    colorscale: [
                        [0, 'rgb(49, 130, 189)'],
                        [0.5, 'rgb(107, 174, 214)'],
                        [1, 'rgb(158, 202, 225)']
                    ],
                    line: {
                        color: 'rgb(8, 48, 107)',
                        width: 1
                    }
                },
                name: 'Fragment Ions',
                hovertemplate:
                    '<b>Fragment m/z:</b> %{x:.4f}<br>' +
                    '<b>Intensity:</b> %{y:.0f}<br>' +
                    '<extra></extra>'
            };

            const layout = {
                title: {
                    text: `MS2 Spectrum - Precursor m/z: ${precursorMZ.toFixed(4)} (RT: ${rt.toFixed(2)}min)`,
                    font: { size: 14, color: '#2c3e50' }
                },
                xaxis: {
                    title: 'Fragment m/z',
                    titlefont: { color: '#2c3e50' },
                    tickfont: { color: '#7f8c8d' },
                    showgrid: true,
                    gridcolor: 'rgba(0,0,0,0.1)'
                },
                yaxis: {
                    title: 'Relative Intensity (%)',
                    titlefont: { color: '#2c3e50' },
                    tickfont: { color: '#7f8c8d' },
                    showgrid: true,
                    gridcolor: 'rgba(0,0,0,0.1)'
                },
                margin: { l: 60, r: 20, b: 60, t: 60 },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(248, 249, 250, 0.8)',
                bargap: 0.1
            };

            const config = {
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'zoom2d', 'zoomIn2d', 'zoomOut2d'],
                displaylogo: false,
                responsive: true
            };

            // Clear the container and create new plot
            document.getElementById('ms2-container').innerHTML = '<div id="ms2-plot" style="height: 100%; width: 100%;"></div>';

            setTimeout(() => {
                Plotly.newPlot('ms2-plot', [trace], layout, config);
            }, 100);
        }

        function resetView() {
            updateStatus('Resetting view...', 'waiting');
            if (ms1Data) {
                createMS1Plot(ms1Data);
                document.getElementById('ms2-container').innerHTML = `
                    <div style="text-align: center;">
                        <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
                        <div>Click on an MS1 data point above to view the corresponding MS2 fragmentation spectrum</div>
                    </div>
                `;
            }
            setTimeout(() => {
                updateStatus('View reset complete', 'ready');
            }, 500);
        }

        function showTutorial() {
            // Create a more interactive tutorial modal
            const tutorialHTML = `
                <div id="tutorial-modal" style="
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.8); z-index: 1000; display: flex;
                    align-items: center; justify-content: center;
                ">
                    <div style="
                        background: white; border-radius: 15px; padding: 30px;
                        max-width: 800px; max-height: 80vh; overflow-y: auto;
                        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                    ">
                        <h2 style="color: #2c3e50; margin-bottom: 20px; text-align: center;">
                            🧬 LC-MS/MS DDA Educational Tutorial
                        </h2>

                        <div style="margin-bottom: 20px;">
                            <h3 style="color: #3498db; border-bottom: 2px solid #3498db; padding-bottom: 5px;">
                                📊 Understanding the 3D MS1 Visualization
                            </h3>
                            <ul style="line-height: 1.6; color: #2c3e50;">
                                <li><strong>X-axis (Retention Time):</strong> Shows when compounds elute from the LC column (0-30 minutes)</li>
                                <li><strong>Y-axis (m/z):</strong> Mass-to-charge ratio of detected ions (100-3000 Da)</li>
                                <li><strong>Z-axis (Intensity):</strong> Signal strength - higher peaks indicate more abundant ions</li>
                                <li><strong>Colors:</strong> Intensity mapping from dark (low) to bright (high)</li>
                                <li><strong>Gaussian Peaks:</strong> Notice how intensity follows bell curves over time</li>
                            </ul>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <h3 style="color: #e74c3c; border-bottom: 2px solid #e74c3c; padding-bottom: 5px;">
                                🎯 DDA (Data Dependent Acquisition) Strategy
                            </h3>
                            <ul style="line-height: 1.6; color: #2c3e50;">
                                <li><strong>TopN Selection:</strong> Only the most intense ions (typically top 5-10) are selected for MS2</li>
                                <li><strong>Dynamic Exclusion:</strong> Once fragmented, ions are temporarily excluded to increase coverage</li>
                                <li><strong>Intensity Threshold:</strong> Low-intensity ions are ignored to focus on abundant species</li>
                                <li><strong>Real-time Decision:</strong> Selection happens automatically during data acquisition</li>
                            </ul>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <h3 style="color: #27ae60; border-bottom: 2px solid #27ae60; padding-bottom: 5px;">
                                🔬 MS2 Fragmentation Spectra
                            </h3>
                            <ul style="line-height: 1.6; color: #2c3e50;">
                                <li><strong>Fragment Ions:</strong> Pieces of the original molecule after collision-induced dissociation</li>
                                <li><strong>Non-Gaussian Distribution:</strong> Fragment intensities don't follow chromatographic patterns</li>
                                <li><strong>Structural Information:</strong> Fragment patterns help identify molecular structure</li>
                                <li><strong>Selective Nature:</strong> Not every MS1 peak has corresponding MS2 data</li>
                            </ul>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <h3 style="color: #f39c12; border-bottom: 2px solid #f39c12; padding-bottom: 5px;">
                                🖱️ Interactive Features
                            </h3>
                            <ul style="line-height: 1.6; color: #2c3e50;">
                                <li><strong>Click MS1 Points:</strong> Click any point in the 3D plot to attempt MS2 visualization</li>
                                <li><strong>3D Navigation:</strong> Drag to rotate, scroll to zoom, shift+drag to pan</li>
                                <li><strong>Hover Information:</strong> Hover over points to see detailed information</li>
                                <li><strong>Red Highlights:</strong> Selected points are highlighted in red for 3 seconds</li>
                                <li><strong>Realistic Simulation:</strong> Many clicks won't show MS2 (realistic DDA behavior)</li>
                            </ul>
                        </div>

                        <div style="background: #ecf0f1; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">💡 Key Learning Points:</h4>
                            <p style="color: #2c3e50; line-height: 1.6; margin: 0;">
                                • MS1 data is continuous and follows chromatographic principles<br>
                                • MS2 data is selective and discontinuous due to DDA strategy<br>
                                • The relationship between MS1 and MS2 is hierarchical but not one-to-one<br>
                                • Understanding this relationship is crucial for proteomics and metabolomics data analysis
                            </p>
                        </div>

                        <button onclick="closeTutorial()" style="
                            background: linear-gradient(135deg, #3498db, #2980b9);
                            color: white; border: none; padding: 12px 30px;
                            border-radius: 8px; cursor: pointer; font-size: 16px;
                            font-weight: 600; width: 100%;
                        ">
                            Start Exploring! 🚀
                        </button>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', tutorialHTML);
        }

        function closeTutorial() {
            const modal = document.getElementById('tutorial-modal');
            if (modal) {
                modal.remove();
            }
        }

        // Add keyboard shortcuts for better user experience
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeTutorial();
            } else if (event.key === 'r' || event.key === 'R') {
                if (event.ctrlKey) {
                    event.preventDefault();
                    generateNewData();
                }
            } else if (event.key === 'h' || event.key === 'H') {
                if (event.ctrlKey) {
                    event.preventDefault();
                    showTutorial();
                }
            }
        });

        // Add window resize handler for responsive plots
        window.addEventListener('resize', function() {
            if (ms1Data) {
                setTimeout(() => {
                    Plotly.Plots.resize('ms1-plot');
                    const ms2Plot = document.getElementById('ms2-plot');
                    if (ms2Plot) {
                        Plotly.Plots.resize('ms2-plot');
                    }
                }, 100);
            }
        });

        // Performance monitoring for educational purposes
        function logPerformanceMetrics() {
            if (ms1Data) {
                console.log('📊 LC-MS/MS Visualization Performance Metrics:');
                console.log(`• MS1 data points: ${ms1Data.retentionTime.length}`);
                console.log(`• Unique compounds: ${new Set(ms1Data.compoundIds).size}`);
                console.log(`• Memory usage: ~${(ms1Data.retentionTime.length * 3 * 8 / 1024).toFixed(1)} KB`);
                console.log(`• Rendering time: <100ms (optimized for education)`);
            }
        }

        // Enhanced Interactive Features
        function highlightSelectedPoint(rt, mz, intensity) {
            // Add a highlight marker to the selected point
            const highlightTrace = {
                x: [rt],
                y: [mz],
                z: [intensity],
                mode: 'markers',
                marker: {
                    size: 8,
                    color: 'red',
                    symbol: 'circle',
                    line: {
                        color: 'darkred',
                        width: 2
                    }
                },
                type: 'scatter3d',
                name: 'Selected Point',
                showlegend: false,
                hovertemplate:
                    '<b>SELECTED POINT</b><br>' +
                    '<b>Retention Time:</b> %{x:.2f} min<br>' +
                    '<b>m/z:</b> %{y:.4f}<br>' +
                    '<b>Intensity:</b> %{z:.0f}<br>' +
                    '<extra></extra>'
            };

            // Add the highlight trace
            Plotly.addTraces('ms1-plot', highlightTrace);

            // Remove highlight after 3 seconds
            setTimeout(() => {
                const plotDiv = document.getElementById('ms1-plot');
                if (plotDiv.data && plotDiv.data.length > 1) {
                    Plotly.deleteTraces('ms1-plot', [1]);
                }
            }, 3000);
        }

        function addHoverEffects() {
            // Add hover effects to MS1 plot
            document.getElementById('ms1-plot').on('plotly_hover', function(data) {
                if (data.points.length > 0) {
                    const point = data.points[0];
                    updateStatus(`Hover: RT=${point.x.toFixed(2)}min, m/z=${point.y.toFixed(4)}, I=${point.z.toFixed(0)}`, 'ready');
                }
            });

            document.getElementById('ms1-plot').on('plotly_unhover', function() {
                updateStatus('Ready - Click MS1 points to explore MS2 spectra', 'ready');
            });
        }

        // Main data generation function
        function generateNewData() {
            updateStatus('Generating new MS1 data...', 'waiting');

            setTimeout(() => {
                try {
                    // Generate MS1 data
                    ms1Data = generateMS1Data();

                    // Simulate DDA selection for educational purposes
                    const selectedPrecursors = simulateDDASelection(ms1Data);
                    console.log(`DDA would select ${selectedPrecursors.length} precursors for MS2`);

                    // Create the 3D plot
                    createMS1Plot(ms1Data);

                    // Add interactive effects
                    setTimeout(() => {
                        addHoverEffects();
                    }, 500);

                    // Reset MS2 display
                    document.getElementById('ms2-container').innerHTML = `
                        <div style="text-align: center;">
                            <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
                            <div>Click on an MS1 data point above to view the corresponding MS2 fragmentation spectrum</div>
                            <div style="margin-top: 10px; font-size: 0.9em; color: #7f8c8d;">
                                <em>DDA simulation: ${selectedPrecursors.length} precursors would be selected for MS2</em>
                            </div>
                        </div>
                    `;

                    updateStatus(`Generated ${ms1Data.retentionTime.length} MS1 data points - Click to explore`, 'ready');
                } catch (error) {
                    console.error('Error generating data:', error);
                    updateStatus('Error generating data', 'error');
                }
            }, 100);
        }
    </script>
</body>
</html>
