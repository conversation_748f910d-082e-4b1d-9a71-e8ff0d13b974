# 数据库转换器
# 将_spectra.rds文件转换为结构化数据库格式

# 加载必要的脚本和包
source("utils/path_manager.R")
source("utils/database_schema.R")
source("utils/data_extractor.R")

# 加载必要的包
load_converter_packages <- function() {
  load_database_packages()
  load_extraction_packages()
}

# 主转换函数
convert_spectra_to_database <- function(cache_dir, output_db_path, project_root = NULL) {
  cat("=== 开始Spectra数据库转换 ===\n")
  cat("缓存目录:", cache_dir, "\n")
  cat("输出数据库:", output_db_path, "\n\n")
  
  # 加载必要的包
  load_converter_packages()
  
  # 检查缓存目录
  if (!dir.exists(cache_dir)) {
    stop("缓存目录不存在:", cache_dir)
  }
  
  # 查找所有_spectra.rds文件
  rds_files <- list.files(cache_dir, pattern = "_spectra\\.rds$", full.names = TRUE)
  
  if (length(rds_files) == 0) {
    stop("在缓存目录中未找到_spectra.rds文件:", cache_dir)
  }
  
  cat("找到", length(rds_files), "个_spectra.rds文件\n\n")
  
  # 创建数据库
  cat("创建数据库结构...\n")
  create_database(output_db_path)
  
  # 连接到数据库
  con <- DBI::dbConnect(RSQLite::SQLite(), output_db_path)
  
  tryCatch({
    # 启用外键约束
    DBI::dbExecute(con, "PRAGMA foreign_keys = ON;")
    
    # 开始事务
    DBI::dbBegin(con)
    
    # 处理每个文件
    for (i in seq_along(rds_files)) {
      rds_file <- rds_files[i]
      file_name <- basename(rds_file)
      
      cat("处理文件", i, "/", length(rds_files), ":", file_name, "\n")
      
      # 提取样本类型和扫描模式
      sample_info <- extract_sample_info_from_filename(file_name)
      
      # 插入文件记录
      file_id <- insert_file_record(con, rds_file, sample_info)
      
      # 提取数据
      extracted_data <- extract_spectra_data(rds_file, file_id)
      
      if (!is.null(extracted_data)) {
        # 更新文件记录的统计信息
        update_file_statistics(con, file_id, extracted_data$file_info)
        
        # 插入MS1数据
        if (!is.null(extracted_data$ms1_data)) {
          insert_ms1_data(con, extracted_data$ms1_data, file_id)
        }
        
        # 插入MS2数据
        if (!is.null(extracted_data$ms2_data)) {
          insert_ms2_data(con, extracted_data$ms2_data, file_id)
        }
      }
      
      cat("  - 文件处理完成\n\n")
    }
    
    # 提交事务
    DBI::dbCommit(con)
    cat("所有数据转换完成！\n")
    
    # 生成转换报告
    generate_conversion_report(con, output_db_path)
    
  }, error = function(e) {
    # 回滚事务
    DBI::dbRollback(con)
    stop("转换过程中发生错误:", e$message)
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
  
  return(TRUE)
}

# 从文件名提取样本信息
extract_sample_info_from_filename <- function(file_name) {
  # 移除.rds扩展名
  base_name <- gsub("_spectra\\.rds$", "", file_name)
  
  # 检测样本类型
  sample_type <- "SAMPLE"  # 默认
  if (grepl("QC", base_name, ignore.case = TRUE)) {
    sample_type <- "QC"
  } else if (grepl("BLANK", base_name, ignore.case = TRUE)) {
    sample_type <- "BLANK"
  } else if (grepl("STD|STAND", base_name, ignore.case = TRUE)) {
    sample_type <- "STD"
  }
  
  # 检测扫描模式
  scan_mode <- "unknown"
  if (grepl("_P_|_pos", base_name, ignore.case = TRUE)) {
    scan_mode <- "positive"
  } else if (grepl("_N_|_neg", base_name, ignore.case = TRUE)) {
    scan_mode <- "negative"
  }
  
  return(list(
    sample_type = sample_type,
    scan_mode = scan_mode
  ))
}

# 插入文件记录
insert_file_record <- function(con, rds_file, sample_info) {
  file_name <- basename(rds_file)
  
  insert_sql <- "
    INSERT INTO data_files (file_name, file_path, sample_type, scan_mode)
    VALUES (?, ?, ?, ?)
  "
  
  DBI::dbExecute(con, insert_sql, list(
    file_name,
    rds_file,
    sample_info$sample_type,
    sample_info$scan_mode
  ))
  
  # 获取插入的file_id
  file_id <- DBI::dbGetQuery(con, "SELECT last_insert_rowid() as id")$id
  
  cat("  - 文件记录已插入，file_id:", file_id, "\n")
  return(file_id)
}

# 更新文件统计信息
update_file_statistics <- function(con, file_id, file_info) {
  update_sql <- "
    UPDATE data_files 
    SET total_spectra = ?, ms1_count = ?, ms2_count = ?, last_updated = CURRENT_TIMESTAMP
    WHERE file_id = ?
  "
  
  DBI::dbExecute(con, update_sql, list(
    file_info$total_spectra,
    file_info$ms1_count,
    file_info$ms2_count,
    file_id
  ))
  
  cat("  - 文件统计信息已更新\n")
}

# 插入MS1数据
insert_ms1_data <- function(con, ms1_data, file_id) {
  if (is.null(ms1_data) || nrow(ms1_data$spectra_data) == 0) {
    return()
  }
  
  cat("  - 插入MS1数据...\n")
  
  # 插入MS1 spectraData
  spectra_df <- ms1_data$spectra_data
  
  # 批量插入spectraData
  DBI::dbWriteTable(con, "ms1_spectra_data", spectra_df, append = TRUE, row.names = FALSE)
  
  # 获取插入的spectrum_id范围
  max_id_result <- DBI::dbGetQuery(con, "SELECT MAX(spectrum_id) as max_id FROM ms1_spectra_data")
  max_spectrum_id <- max_id_result$max_id
  min_spectrum_id <- max_spectrum_id - nrow(spectra_df) + 1
  
  # 更新peaks数据中的spectrum_id
  peaks_df <- ms1_data$peaks_data
  if (nrow(peaks_df) > 0) {
    # 创建spectrum_id映射
    spectrum_id_mapping <- min_spectrum_id:max_spectrum_id
    
    # 更新peaks_df中的spectrum_id
    peaks_df$spectrum_id <- spectrum_id_mapping[peaks_df$spectrum_id]
    
    # 批量插入peaksData
    DBI::dbWriteTable(con, "ms1_peaks_data", peaks_df, append = TRUE, row.names = FALSE)
  }
  
  cat("    - MS1 spectraData:", nrow(spectra_df), "行\n")
  cat("    - MS1 peaksData:", nrow(peaks_df), "行\n")
}

# 插入MS2数据
insert_ms2_data <- function(con, ms2_data, file_id) {
  if (is.null(ms2_data) || nrow(ms2_data$spectra_data) == 0) {
    return()
  }
  
  cat("  - 插入MS2数据...\n")
  
  # 插入MS2 spectraData
  spectra_df <- ms2_data$spectra_data
  
  # 批量插入spectraData
  DBI::dbWriteTable(con, "ms2_spectra_data", spectra_df, append = TRUE, row.names = FALSE)
  
  # 获取插入的spectrum_id范围
  max_id_result <- DBI::dbGetQuery(con, "SELECT MAX(spectrum_id) as max_id FROM ms2_spectra_data")
  max_spectrum_id <- max_id_result$max_id
  min_spectrum_id <- max_spectrum_id - nrow(spectra_df) + 1
  
  # 更新peaks数据中的spectrum_id
  peaks_df <- ms2_data$peaks_data
  if (nrow(peaks_df) > 0) {
    # 创建spectrum_id映射
    spectrum_id_mapping <- min_spectrum_id:max_spectrum_id
    
    # 更新peaks_df中的spectrum_id
    peaks_df$spectrum_id <- spectrum_id_mapping[peaks_df$spectrum_id]
    
    # 批量插入peaksData
    DBI::dbWriteTable(con, "ms2_peaks_data", peaks_df, append = TRUE, row.names = FALSE)
  }
  
  cat("    - MS2 spectraData:", nrow(spectra_df), "行\n")
  cat("    - MS2 peaksData:", nrow(peaks_df), "行\n")
}

# 生成转换报告
generate_conversion_report <- function(con, db_path) {
  cat("\n=== 转换报告 ===\n")
  
  # 获取数据库信息
  db_info <- get_database_info(db_path)
  
  cat("数据库文件:", db_path, "\n")
  cat("数据库大小:", round(file.size(db_path) / 1024 / 1024, 2), "MB\n")
  cat("表统计:\n")
  
  for (table_name in names(db_info$table_counts)) {
    count <- db_info$table_counts[[table_name]]
    cat("  -", table_name, ":", count, "行\n")
  }
  
  # 获取样本类型统计
  sample_stats <- DBI::dbGetQuery(con, "
    SELECT sample_type, scan_mode, COUNT(*) as count 
    FROM data_files 
    GROUP BY sample_type, scan_mode
    ORDER BY sample_type, scan_mode
  ")
  
  if (nrow(sample_stats) > 0) {
    cat("\n样本统计:\n")
    for (i in 1:nrow(sample_stats)) {
      row <- sample_stats[i, ]
      cat("  -", row$sample_type, "(", row$scan_mode, "):", row$count, "个文件\n")
    }
  }
  
  cat("\n转换完成！\n")
}

# 主函数：转换项目中的所有数据
convert_project_spectra <- function(project_root = NULL) {
  # 使用路径管理服务获取当前项目路径
  if (is.null(project_root)) {
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      stop("无法获取项目路径，请确保项目已正确设置")
    }
  }

  # 使用路径管理服务构建项目相对路径
  cache_dir <- get_project_absolute_path("data", "cache", "spectra_v2")
  results_dir <- get_project_absolute_path("results")

  # 确保results目录存在
  if (!validate_path(results_dir, create_if_missing = TRUE)) {
    stop("无法创建结果目录:", results_dir)
  }

  # 构建数据库文件路径
  output_db_path <- file.path(results_dir, "spectra.db")

  cat("项目根目录:", project_root, "\n")
  cat("缓存目录:", cache_dir, "\n")
  cat("输出数据库:", output_db_path, "\n\n")

  # 执行转换
  result <- convert_spectra_to_database(cache_dir, output_db_path, project_root)

  return(list(
    success = result,
    database_path = output_db_path
  ))
}
