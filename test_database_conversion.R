# 测试数据库转换脚本
# 测试_spectra.rds文件到数据库的转换过程

# 设置工作目录和加载脚本
setwd(".")
source("utils/database_converter.R")
source("utils/data_validator.R")

# 主测试函数
test_database_conversion <- function() {
  cat("=== 开始测试数据库转换 ===\n\n")
  
  # 设置测试参数
  project_root <- "test/test4"
  cache_dir <- file.path(project_root, "data", "cache", "spectra_v2")
  results_dir <- file.path(project_root, "results")
  test_db_path <- file.path(results_dir, "test_ms_database.sqlite")
  
  # 确保results目录存在
  if (!dir.exists(results_dir)) {
    dir.create(results_dir, recursive = TRUE)
    cat("创建结果目录:", results_dir, "\n")
  }
  
  # 删除已存在的测试数据库
  if (file.exists(test_db_path)) {
    file.remove(test_db_path)
    cat("删除已存在的测试数据库\n")
  }
  
  cat("测试参数:\n")
  cat("  - 项目根目录:", project_root, "\n")
  cat("  - 缓存目录:", cache_dir, "\n")
  cat("  - 测试数据库:", test_db_path, "\n\n")
  
  # 检查缓存目录和文件
  if (!dir.exists(cache_dir)) {
    cat("错误: 缓存目录不存在:", cache_dir, "\n")
    return(FALSE)
  }
  
  rds_files <- list.files(cache_dir, pattern = "_spectra\\.rds$", full.names = TRUE)
  if (length(rds_files) == 0) {
    cat("错误: 在缓存目录中未找到_spectra.rds文件\n")
    return(FALSE)
  }
  
  cat("找到", length(rds_files), "个_spectra.rds文件:\n")
  for (i in seq_along(rds_files)) {
    cat("  ", i, ".", basename(rds_files[i]), "\n")
  }
  cat("\n")
  
  # 执行转换
  cat("开始执行数据库转换...\n")
  conversion_start_time <- Sys.time()
  
  tryCatch({
    # 执行转换
    result <- convert_spectra_to_database(cache_dir, test_db_path)
    
    conversion_end_time <- Sys.time()
    conversion_duration <- as.numeric(difftime(conversion_end_time, conversion_start_time, units = "secs"))
    
    cat("\n转换完成! 耗时:", round(conversion_duration, 2), "秒\n\n")
    
    # 验证转换结果
    cat("开始验证转换结果...\n")
    validation_result <- validate_database_integrity(test_db_path)
    
    # 生成详细的测试报告
    generate_test_report(test_db_path, validation_result, conversion_duration, results_dir)
    
    # 执行数据查询测试
    cat("\n开始数据查询测试...\n")
    query_test_result <- test_database_queries(test_db_path)
    
    # 总结测试结果
    cat("\n=== 测试总结 ===\n")
    cat("转换状态:", if (result) "成功" else "失败", "\n")
    cat("验证状态:", if (validation_result$valid) "通过" else "失败", "\n")
    cat("查询测试:", if (query_test_result) "通过" else "失败", "\n")
    cat("总体结果:", if (result && validation_result$valid && query_test_result) "成功" else "失败", "\n")
    
    return(result && validation_result$valid && query_test_result)
    
  }, error = function(e) {
    cat("转换过程中发生错误:", e$message, "\n")
    return(FALSE)
  })
}

# 生成测试报告
generate_test_report <- function(db_path, validation_result, conversion_duration, results_dir) {
  cat("生成测试报告...\n")
  
  # 获取数据库基本信息
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 获取表统计信息
    table_stats <- list()
    tables <- c("data_files", "ms1_spectra_data", "ms1_peaks_data", "ms2_spectra_data", "ms2_peaks_data")
    
    for (table in tables) {
      count_result <- DBI::dbGetQuery(con, paste("SELECT COUNT(*) as count FROM", table))
      table_stats[[table]] <- count_result$count
    }
    
    # 获取样本统计信息
    sample_stats <- DBI::dbGetQuery(con, "
      SELECT sample_type, scan_mode, COUNT(*) as file_count,
             SUM(total_spectra) as total_spectra,
             SUM(ms1_count) as total_ms1,
             SUM(ms2_count) as total_ms2
      FROM data_files 
      GROUP BY sample_type, scan_mode
      ORDER BY sample_type, scan_mode
    ")
    
    # 获取数据质量统计
    quality_stats <- list()
    
    # MS1数据质量
    ms1_quality <- DBI::dbGetQuery(con, "
      SELECT 
        COUNT(*) as total_spectra,
        AVG(peaks_count) as avg_peaks,
        MIN(rtime) as min_rtime,
        MAX(rtime) as max_rtime,
        AVG(tot_ion_current) as avg_tic
      FROM ms1_spectra_data
    ")
    quality_stats$ms1 <- ms1_quality
    
    # MS2数据质量
    ms2_quality <- DBI::dbGetQuery(con, "
      SELECT 
        COUNT(*) as total_spectra,
        AVG(peaks_count) as avg_peaks,
        MIN(rtime) as min_rtime,
        MAX(rtime) as max_rtime,
        AVG(precursor_mz) as avg_precursor_mz
      FROM ms2_spectra_data
    ")
    quality_stats$ms2 <- ms2_quality
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
  
  # 创建测试报告
  test_report <- list(
    test_info = list(
      timestamp = format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
      database_path = db_path,
      database_size_mb = round(file.size(db_path) / 1024 / 1024, 2),
      conversion_duration_seconds = conversion_duration
    ),
    table_statistics = table_stats,
    sample_statistics = sample_stats,
    quality_statistics = quality_stats,
    validation_result = validation_result
  )
  
  # 保存报告
  report_file <- file.path(results_dir, "database_conversion_test_report.json")
  jsonlite::write_json(test_report, report_file, pretty = TRUE, auto_unbox = TRUE)
  
  cat("测试报告已保存到:", report_file, "\n")
  
  # 打印关键统计信息
  cat("\n=== 数据库统计信息 ===\n")
  cat("数据库大小:", test_report$test_info$database_size_mb, "MB\n")
  cat("转换耗时:", round(conversion_duration, 2), "秒\n")
  cat("\n表统计:\n")
  for (table in names(table_stats)) {
    cat("  -", table, ":", table_stats[[table]], "行\n")
  }
  
  if (nrow(sample_stats) > 0) {
    cat("\n样本统计:\n")
    for (i in 1:nrow(sample_stats)) {
      row <- sample_stats[i, ]
      cat("  -", row$sample_type, "(", row$scan_mode, "):", 
          row$file_count, "文件,", row$total_spectra, "谱图\n")
    }
  }
  
  return(test_report)
}

# 测试数据库查询功能
test_database_queries <- function(db_path) {
  cat("执行数据库查询测试...\n")
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 测试1: 基本查询
    cat("  1. 测试基本查询...\n")
    file_count <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM data_files")
    cat("     - 文件数量:", file_count$count, "\n")
    
    # 测试2: 连接查询
    cat("  2. 测试连接查询...\n")
    join_result <- DBI::dbGetQuery(con, "
      SELECT df.file_name, COUNT(ms1.spectrum_id) as ms1_count
      FROM data_files df
      LEFT JOIN ms1_spectra_data ms1 ON df.file_id = ms1.file_id
      GROUP BY df.file_id, df.file_name
      LIMIT 5
    ")
    cat("     - 连接查询结果行数:", nrow(join_result), "\n")
    
    # 测试3: 聚合查询
    cat("  3. 测试聚合查询...\n")
    agg_result <- DBI::dbGetQuery(con, "
      SELECT 
        sample_type,
        COUNT(*) as file_count,
        SUM(total_spectra) as total_spectra,
        AVG(total_spectra) as avg_spectra_per_file
      FROM data_files
      GROUP BY sample_type
    ")
    cat("     - 聚合查询结果行数:", nrow(agg_result), "\n")
    
    # 测试4: 复杂查询 - 获取每个文件的峰数据统计
    cat("  4. 测试复杂查询...\n")
    complex_result <- DBI::dbGetQuery(con, "
      SELECT 
        df.file_name,
        df.sample_type,
        COUNT(DISTINCT ms1.spectrum_id) as ms1_spectra_count,
        COUNT(DISTINCT mp1.peak_id) as ms1_peaks_count,
        COUNT(DISTINCT ms2.spectrum_id) as ms2_spectra_count,
        COUNT(DISTINCT mp2.peak_id) as ms2_peaks_count
      FROM data_files df
      LEFT JOIN ms1_spectra_data ms1 ON df.file_id = ms1.file_id
      LEFT JOIN ms1_peaks_data mp1 ON ms1.spectrum_id = mp1.spectrum_id
      LEFT JOIN ms2_spectra_data ms2 ON df.file_id = ms2.file_id
      LEFT JOIN ms2_peaks_data mp2 ON ms2.spectrum_id = mp2.spectrum_id
      GROUP BY df.file_id, df.file_name, df.sample_type
      LIMIT 3
    ")
    cat("     - 复杂查询结果行数:", nrow(complex_result), "\n")
    
    # 测试5: 数据范围查询
    cat("  5. 测试数据范围查询...\n")
    range_result <- DBI::dbGetQuery(con, "
      SELECT 
        MIN(mz) as min_mz,
        MAX(mz) as max_mz,
        AVG(mz) as avg_mz,
        COUNT(*) as peak_count
      FROM (
        SELECT mz FROM ms1_peaks_data
        UNION ALL
        SELECT mz FROM ms2_peaks_data
      )
    ")
    cat("     - m/z范围:", round(range_result$min_mz, 4), "-", round(range_result$max_mz, 4), "\n")
    cat("     - 总峰数:", range_result$peak_count, "\n")
    
    cat("  查询测试完成\n")
    return(TRUE)
    
  }, error = function(e) {
    cat("  查询测试失败:", e$message, "\n")
    return(FALSE)
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
}

# 运行测试
if (!interactive()) {
  # 如果是非交互模式运行，直接执行测试
  test_result <- test_database_conversion()
  if (test_result) {
    cat("\n所有测试通过！\n")
    quit(status = 0)
  } else {
    cat("\n测试失败！\n")
    quit(status = 1)
  }
} else {
  # 交互模式下，提供测试函数供调用
  cat("测试脚本已加载。运行 test_database_conversion() 开始测试。\n")
}
