# Shiny应用数据库转换集成说明

## 概述

本文档说明了如何在实验室实时质控系统的Shiny应用中使用新集成的数据库转换功能。该功能将原有的_spectra.rds文件转换为结构化的SQLite数据库格式，便于后续的质控分析和数据查询。

## 功能特点

### 1. 集成位置
- **模块位置**: 工作区 → 数据管理 → 数据转换
- **UI组件**: 新增数据库转换控制卡片和统计显示
- **后端逻辑**: 异步处理，支持进度监控和错误处理

### 2. 主要功能
- **自动转换**: 在常规数据转换完成后自动触发数据库转换
- **手动转换**: 独立的数据库转换按钮，可单独执行
- **状态监控**: 实时显示转换状态和进度
- **统计显示**: 显示已转换的数据库文件数量
- **错误处理**: 完善的错误提示和日志记录

## 使用方法

### 方法一：自动数据库转换（推荐）

1. **启动应用**
   - 打开Shiny应用，选择或创建项目
   - 进入工作区的"数据管理"标签页

2. **配置转换选项**
   - 在"数据转换"模块中找到"数据转换"控制卡片
   - 确保"转换为数据库格式"选项已勾选（默认启用）

3. **开始转换**
   - 点击"开始转换"按钮
   - 系统将先进行常规的mzML转换和Spectra对象生成
   - 转换完成后自动启动数据库转换

4. **监控进度**
   - 观察转换进度条和状态信息
   - 数据库转换状态会在相应卡片中显示

### 方法二：独立数据库转换

1. **前提条件**
   - 确保项目中已有_spectra.rds文件
   - 这些文件通常在`项目路径/data/cache/spectra_v2/`目录下

2. **执行转换**
   - 在"数据转换"模块中找到"数据库转换"控制卡片
   - 点击"转换为数据库"按钮

3. **查看结果**
   - 转换状态会实时更新
   - 成功后会显示数据库文件路径
   - 统计卡片中的"数据库文件数"会更新

## 界面说明

### 统计卡片
- **已转换文件数**: 显示已完成mzML转换的文件数量
- **数据库文件数**: 显示已生成的数据库文件数量（新增）

### 控制卡片
- **数据转换**: 原有功能，新增"转换为数据库格式"选项
- **数据库转换**: 新增功能，专门用于数据库转换

### 状态显示
- **转换进度**: 显示整体转换进度
- **数据库转换状态**: 显示数据库转换的具体状态

## 输出文件

### 数据库文件位置
```
项目路径/
├── spectra_database.db          # 主数据库文件
├── data/
│   └── cache/
│       └── spectra_v2/
│           ├── file1_spectra.rds
│           ├── file2_spectra.rds
│           └── ...
└── logs/
    └── database_conversion.log  # 转换日志
```

### 数据库结构
数据库包含5个主要表：
- `data_files`: 文件信息表
- `ms1_spectra_data`: MS1谱图数据表
- `ms1_peaks_data`: MS1峰数据表
- `ms2_spectra_data`: MS2谱图数据表
- `ms2_peaks_data`: MS2峰数据表

## 注意事项

### 1. 系统要求
- 确保已安装必要的R包：Spectra, MsExperiment, RSQLite, DBI
- 有足够的磁盘空间存储数据库文件

### 2. 性能考虑
- 数据库转换是异步操作，不会阻塞界面
- 大型数据集转换可能需要较长时间
- 转换过程中避免关闭应用

### 3. 错误处理
- 如果转换失败，检查日志文件获取详细错误信息
- 确保_spectra.rds文件完整且可读
- 检查磁盘空间是否充足

### 4. 数据完整性
- 转换完成后会自动进行数据完整性验证
- 如发现问题，会在日志中记录详细信息

## 故障排除

### 常见问题

1. **"未找到_spectra.rds文件"**
   - 确保先完成常规数据转换
   - 检查缓存目录是否存在

2. **"数据库转换失败"**
   - 查看日志文件获取详细错误
   - 检查磁盘空间和文件权限

3. **转换状态一直显示"正在转换"**
   - 大文件转换需要时间，请耐心等待
   - 如长时间无响应，可刷新页面重试

### 日志查看
转换过程的详细日志会记录在：
- 应用控制台输出
- 项目日志文件（如果配置）

## 后续使用

转换完成的数据库文件可以用于：
- 高效的质控数据查询
- 批量数据分析
- 自定义报告生成
- 与其他分析工具集成

详细的数据库查询示例请参考`README_数据库转换系统.md`文档。
