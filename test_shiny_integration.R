# 测试Shiny应用数据库转换集成
# 验证数据库转换功能是否正确集成到Shiny应用中

cat("=== 测试Shiny应用数据库转换集成 ===\n")

# 1. 检查必要的文件是否存在
cat("\n1. 检查必要文件...\n")

required_files <- c(
  "utils/database_converter.R",
  "utils/data_validator.R", 
  "utils/database_schema.R",
  "utils/data_extractor.R",
  "server/workspace_server.R",
  "ui/workspace_ui.R"
)

all_files_exist <- TRUE
for (file in required_files) {
  if (file.exists(file)) {
    cat("✓", file, "存在\n")
  } else {
    cat("✗", file, "不存在\n")
    all_files_exist <- FALSE
  }
}

if (!all_files_exist) {
  stop("缺少必要文件，请检查项目结构")
}

# 2. 检查数据库转换模块是否可以正常加载
cat("\n2. 测试数据库转换模块加载...\n")

tryCatch({
  source("utils/database_converter.R", encoding = "UTF-8")
  cat("✓ database_converter.R 加载成功\n")
  
  source("utils/data_validator.R", encoding = "UTF-8")
  cat("✓ data_validator.R 加载成功\n")
  
  # 检查关键函数是否存在
  if (exists("convert_project_spectra")) {
    cat("✓ convert_project_spectra 函数存在\n")
  } else {
    cat("✗ convert_project_spectra 函数不存在\n")
  }
  
  if (exists("validate_database_integrity")) {
    cat("✓ validate_database_integrity 函数存在\n")
  } else {
    cat("✗ validate_database_integrity 函数不存在\n")
  }
  
}, error = function(e) {
  cat("✗ 数据库转换模块加载失败:", e$message, "\n")
})

# 3. 检查UI文件中的数据库转换元素
cat("\n3. 检查UI集成...\n")

ui_content <- readLines("ui/workspace_ui.R", encoding = "UTF-8")

# 检查关键UI元素
ui_checks <- list(
  "数据库转换按钮" = any(grepl("start_database_conversion", ui_content)),
  "数据库转换状态" = any(grepl("database_conversion_status", ui_content)),
  "数据库文件计数" = any(grepl("database_files_count", ui_content)),
  "数据库转换选项" = any(grepl("enable_database_conversion", ui_content))
)

for (check_name in names(ui_checks)) {
  if (ui_checks[[check_name]]) {
    cat("✓", check_name, "已添加到UI\n")
  } else {
    cat("✗", check_name, "未找到\n")
  }
}

# 4. 检查服务器端集成
cat("\n4. 检查服务器端集成...\n")

server_content <- readLines("server/workspace_server.R", encoding = "UTF-8")

# 检查关键服务器端元素
server_checks <- list(
  "数据库转换模块加载" = any(grepl("database_converter\\.R", server_content)),
  "数据库转换事件处理" = any(grepl("observeEvent.*start_database_conversion", server_content)),
  "数据库转换状态变量" = any(grepl("database_conversion_status", server_content)),
  "自动数据库转换逻辑" = any(grepl("enable_database_conversion", server_content)),
  "数据库文件计数输出" = any(grepl("output\\$database_files_count", server_content))
)

for (check_name in names(server_checks)) {
  if (server_checks[[check_name]]) {
    cat("✓", check_name, "已实现\n")
  } else {
    cat("✗", check_name, "未找到\n")
  }
}

# 5. 检查xcms移除情况
cat("\n5. 检查xcms移除情况...\n")

xcms_checks <- list(
  "server中无xcms库调用" = !any(grepl("library\\(xcms\\)", server_content)),
  "server中无chromatogram调用" = !any(grepl("chromatogram\\(", server_content)),
  "UI中EIC按钮已禁用" = any(grepl("disabled.*=.*TRUE", ui_content))
)

for (check_name in names(xcms_checks)) {
  if (xcms_checks[[check_name]]) {
    cat("✓", check_name, "\n")
  } else {
    cat("✗", check_name, "\n")
  }
}

# 6. 生成集成报告
cat("\n=== 集成测试报告 ===\n")

total_checks <- length(ui_checks) + length(server_checks) + length(xcms_checks)
passed_checks <- sum(unlist(ui_checks)) + sum(unlist(server_checks)) + sum(unlist(xcms_checks))

cat("总检查项:", total_checks, "\n")
cat("通过检查:", passed_checks, "\n")
cat("通过率:", round(passed_checks/total_checks * 100, 1), "%\n")

if (passed_checks == total_checks) {
  cat("\n🎉 所有检查通过！数据库转换功能已成功集成到Shiny应用中。\n")
} else {
  cat("\n⚠️  部分检查未通过，请检查相关实现。\n")
}

# 7. 使用建议
cat("\n=== 使用建议 ===\n")
cat("1. 启动Shiny应用后，进入工作区的'数据管理'标签页\n")
cat("2. 在'数据转换'模块中，可以看到新增的数据库转换功能\n")
cat("3. 勾选'转换为数据库格式'选项，然后点击'开始转换'进行常规转换\n")
cat("4. 或者直接点击'转换为数据库'按钮进行独立的数据库转换\n")
cat("5. 转换状态和进度会在界面上实时显示\n")
cat("6. 数据库文件计数会在统计卡片中显示\n")

cat("\n测试完成！\n")
