# 质谱数据库转换系统使用示例
# 演示如何使用转换系统将_spectra.rds文件转换为数据库格式

# ============================================================================
# 示例1: 基本转换流程
# ============================================================================

cat("=== 示例1: 基本转换流程 ===\n")

# 加载转换器
source("utils/database_converter.R")

# 方法1: 使用默认项目路径转换
cat("方法1: 转换默认项目数据\n")
result <- convert_project_spectra("test/test4")

if (result$success) {
  cat("✓ 转换成功！\n")
  cat("  数据库位置:", result$database_path, "\n")
} else {
  cat("✗ 转换失败！\n")
}

# 方法2: 指定自定义路径
cat("\n方法2: 指定自定义路径\n")
cache_dir <- "test/test4/data/cache/spectra_v2"
output_db <- "test/test4/results/custom_ms_database.sqlite"

if (dir.exists(cache_dir)) {
  convert_spectra_to_database(cache_dir, output_db)
  cat("✓ 自定义路径转换完成\n")
} else {
  cat("✗ 缓存目录不存在:", cache_dir, "\n")
}

# ============================================================================
# 示例2: 数据验证
# ============================================================================

cat("\n=== 示例2: 数据验证 ===\n")

# 加载验证器
source("utils/data_validator.R")

# 验证转换后的数据库
db_path <- "test/test4/results/ms_database.sqlite"

if (file.exists(db_path)) {
  cat("验证数据库:", db_path, "\n")
  validation_result <- validate_database_integrity(db_path)
  
  if (validation_result$valid) {
    cat("✓ 数据库验证通过\n")
  } else {
    cat("✗ 数据库验证失败\n")
    cat("错误信息:\n")
    for (error in validation_result$errors) {
      cat("  -", error, "\n")
    }
  }
  
  if (length(validation_result$warnings) > 0) {
    cat("警告信息:\n")
    for (warning in validation_result$warnings) {
      cat("  -", warning, "\n")
    }
  }
} else {
  cat("✗ 数据库文件不存在:", db_path, "\n")
}

# ============================================================================
# 示例3: 数据库查询
# ============================================================================

cat("\n=== 示例3: 数据库查询 ===\n")

if (file.exists(db_path)) {
  # 连接数据库
  library(DBI)
  library(RSQLite)
  
  con <- dbConnect(SQLite(), db_path)
  
  tryCatch({
    cat("连接到数据库:", db_path, "\n")
    
    # 查询1: 基本统计信息
    cat("\n1. 基本统计信息:\n")
    file_stats <- dbGetQuery(con, "
      SELECT 
        COUNT(*) as total_files,
        SUM(total_spectra) as total_spectra,
        SUM(ms1_count) as total_ms1,
        SUM(ms2_count) as total_ms2
      FROM data_files
    ")
    
    cat("   总文件数:", file_stats$total_files, "\n")
    cat("   总谱图数:", file_stats$total_spectra, "\n")
    cat("   MS1谱图数:", file_stats$total_ms1, "\n")
    cat("   MS2谱图数:", file_stats$total_ms2, "\n")
    
    # 查询2: 样本类型分布
    cat("\n2. 样本类型分布:\n")
    sample_dist <- dbGetQuery(con, "
      SELECT sample_type, scan_mode, COUNT(*) as file_count
      FROM data_files
      GROUP BY sample_type, scan_mode
      ORDER BY sample_type, scan_mode
    ")
    
    for (i in 1:nrow(sample_dist)) {
      row <- sample_dist[i, ]
      cat("   ", row$sample_type, "(", row$scan_mode, "):", row$file_count, "个文件\n")
    }
    
    # 查询3: 数据质量概览
    cat("\n3. 数据质量概览:\n")
    
    # MS1数据质量
    ms1_quality <- dbGetQuery(con, "
      SELECT 
        COUNT(*) as spectra_count,
        ROUND(AVG(peaks_count), 1) as avg_peaks,
        ROUND(MIN(rtime), 2) as min_rtime,
        ROUND(MAX(rtime), 2) as max_rtime,
        ROUND(AVG(tot_ion_current), 0) as avg_tic
      FROM ms1_spectra_data
    ")
    
    cat("   MS1数据:\n")
    cat("     谱图数:", ms1_quality$spectra_count, "\n")
    cat("     平均峰数:", ms1_quality$avg_peaks, "\n")
    cat("     保留时间范围:", ms1_quality$min_rtime, "-", ms1_quality$max_rtime, "秒\n")
    cat("     平均TIC:", ms1_quality$avg_tic, "\n")
    
    # MS2数据质量
    ms2_quality <- dbGetQuery(con, "
      SELECT 
        COUNT(*) as spectra_count,
        ROUND(AVG(peaks_count), 1) as avg_peaks,
        ROUND(AVG(precursor_mz), 2) as avg_precursor_mz,
        ROUND(AVG(collision_energy), 1) as avg_ce
      FROM ms2_spectra_data
      WHERE precursor_mz IS NOT NULL
    ")
    
    if (nrow(ms2_quality) > 0 && ms2_quality$spectra_count > 0) {
      cat("   MS2数据:\n")
      cat("     谱图数:", ms2_quality$spectra_count, "\n")
      cat("     平均峰数:", ms2_quality$avg_peaks, "\n")
      cat("     平均前体离子m/z:", ms2_quality$avg_precursor_mz, "\n")
      cat("     平均碰撞能:", ms2_quality$avg_ce, "\n")
    }
    
    # 查询4: 峰数据统计
    cat("\n4. 峰数据统计:\n")
    peak_stats <- dbGetQuery(con, "
      SELECT 
        'MS1' as level,
        COUNT(*) as peak_count,
        ROUND(MIN(mz), 4) as min_mz,
        ROUND(MAX(mz), 4) as max_mz,
        ROUND(AVG(intensity), 0) as avg_intensity
      FROM ms1_peaks_data
      UNION ALL
      SELECT 
        'MS2' as level,
        COUNT(*) as peak_count,
        ROUND(MIN(mz), 4) as min_mz,
        ROUND(MAX(mz), 4) as max_mz,
        ROUND(AVG(intensity), 0) as avg_intensity
      FROM ms2_peaks_data
    ")
    
    for (i in 1:nrow(peak_stats)) {
      row <- peak_stats[i, ]
      cat("   ", row$level, "峰数据:\n")
      cat("     峰数量:", row$peak_count, "\n")
      cat("     m/z范围:", row$min_mz, "-", row$max_mz, "\n")
      cat("     平均强度:", row$avg_intensity, "\n")
    }
    
  }, finally = {
    dbDisconnect(con)
  })
  
} else {
  cat("✗ 数据库文件不存在，无法执行查询\n")
}

# ============================================================================
# 示例4: 单个文件分析
# ============================================================================

cat("\n=== 示例4: 单个文件分析 ===\n")

# 加载数据提取器
source("utils/data_extractor.R")

# 分析单个_spectra.rds文件
cache_dir <- "test/test4/data/cache/spectra_v2"
if (dir.exists(cache_dir)) {
  rds_files <- list.files(cache_dir, pattern = "_spectra\\.rds$", full.names = TRUE)
  
  if (length(rds_files) > 0) {
    # 分析第一个文件
    sample_file <- rds_files[1]
    cat("分析文件:", basename(sample_file), "\n")
    
    extracted_data <- extract_spectra_data(sample_file, file_id = 999)
    
    if (!is.null(extracted_data)) {
      cat("✓ 数据提取成功\n")
      cat("  文件信息:\n")
      cat("    总谱图数:", extracted_data$file_info$total_spectra, "\n")
      cat("    MS1谱图数:", extracted_data$file_info$ms1_count, "\n")
      cat("    MS2谱图数:", extracted_data$file_info$ms2_count, "\n")
      
      if (!is.null(extracted_data$ms1_data)) {
        cat("  MS1数据:\n")
        cat("    spectraData行数:", nrow(extracted_data$ms1_data$spectra_data), "\n")
        cat("    peaksData行数:", nrow(extracted_data$ms1_data$peaks_data), "\n")
      }
      
      if (!is.null(extracted_data$ms2_data)) {
        cat("  MS2数据:\n")
        cat("    spectraData行数:", nrow(extracted_data$ms2_data$spectra_data), "\n")
        cat("    peaksData行数:", nrow(extracted_data$ms2_data$peaks_data), "\n")
      }
    } else {
      cat("✗ 数据提取失败\n")
    }
  } else {
    cat("✗ 在缓存目录中未找到_spectra.rds文件\n")
  }
} else {
  cat("✗ 缓存目录不存在:", cache_dir, "\n")
}

# ============================================================================
# 示例5: 批量处理和报告生成
# ============================================================================

cat("\n=== 示例5: 批量处理和报告生成 ===\n")

# 运行完整的测试和报告生成
if (file.exists("test_database_conversion.R")) {
  cat("运行完整测试...\n")
  source("test_database_conversion.R")
  
  # 注意：在实际使用中，您可以调用 test_database_conversion() 函数
  # test_result <- test_database_conversion()
  cat("测试脚本已加载，可以调用 test_database_conversion() 运行完整测试\n")
} else {
  cat("✗ 测试脚本不存在\n")
}

cat("\n=== 使用示例完成 ===\n")
cat("更多详细信息请参考 README_数据库转换系统.md\n")
