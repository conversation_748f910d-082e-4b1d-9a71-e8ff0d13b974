# 测试数据库存储修复
# 验证函数定义是否正确，不再有未使用参数的警告

cat("=== 测试数据库存储修复 ===\n\n")

# 1. 测试convert_spectra_to_database函数定义
cat("1. 检查convert_spectra_to_database函数定义...\n")
tryCatch({
  source("utils/database_converter.R", encoding = "UTF-8")
  
  if (exists("convert_spectra_to_database")) {
    args <- formals(convert_spectra_to_database)
    cat("✓ convert_spectra_to_database函数存在\n")
    cat("  参数:", names(args), "\n")
    
    # 检查是否还有project_root参数
    if ("project_root" %in% names(args)) {
      cat("✗ 仍然包含未使用的project_root参数\n")
    } else {
      cat("✓ 已移除未使用的project_root参数\n")
    }
  } else {
    cat("✗ convert_spectra_to_database函数不存在\n")
  }
}, error = function(e) {
  cat("✗ 加载database_converter.R失败:", e$message, "\n")
})

cat("\n")

# 2. 测试store_single_file_to_database函数定义
cat("2. 检查store_single_file_to_database函数定义...\n")
tryCatch({
  source("server/workspace_server.R", encoding = "UTF-8")
  
  if (exists("store_single_file_to_database")) {
    args <- formals(store_single_file_to_database)
    cat("✓ store_single_file_to_database函数存在\n")
    cat("  参数:", names(args), "\n")
    
    # 检查是否还有project_root参数
    if ("project_root" %in% names(args)) {
      cat("✗ 仍然包含未使用的project_root参数\n")
    } else {
      cat("✓ 已移除未使用的project_root参数\n")
    }
  } else {
    cat("✗ store_single_file_to_database函数不存在\n")
  }
}, error = function(e) {
  cat("✗ 加载workspace_server.R失败:", e$message, "\n")
})

cat("\n")

# 3. 测试函数调用语法
cat("3. 测试函数调用语法...\n")
tryCatch({
  # 模拟函数调用（不实际执行）
  test_cache_dir <- "test/cache"
  test_db_path <- "test/test.db"
  test_rds_file <- "test/test.rds"
  
  # 检查convert_spectra_to_database调用语法
  if (exists("convert_spectra_to_database")) {
    # 这里只检查语法，不实际执行
    call_expr <- quote(convert_spectra_to_database(test_cache_dir, test_db_path))
    cat("✓ convert_spectra_to_database调用语法正确\n")
  }
  
  # 检查store_single_file_to_database调用语法
  if (exists("store_single_file_to_database")) {
    # 这里只检查语法，不实际执行
    call_expr <- quote(store_single_file_to_database(test_rds_file))
    cat("✓ store_single_file_to_database调用语法正确\n")
  }
  
}, error = function(e) {
  cat("✗ 函数调用语法错误:", e$message, "\n")
})

cat("\n=== 测试完成 ===\n")
cat("如果所有检查都通过，说明数据库存储的'参数没有用'问题已修复\n")
